<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  export interface Tab {
    id: string;
    label: string;
    disabled?: boolean;
  }

  export let tabs: Tab[] = [];
  export let activeTab: string = '';

  const dispatch = createEventDispatcher<{
    change: { tabId: string };
  }>();

  function handleTabClick(tabId: string) {
    if (tabs.find(tab => tab.id === tabId)?.disabled) {
      return;
    }
    
    activeTab = tabId;
    dispatch('change', { tabId });
  }

  function handleKeyDown(event: KeyboardEvent, tabId: string) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleTabClick(tabId);
    }
  }
</script>

<div class="tab-navigation">
  {#each tabs as tab (tab.id)}
    <button
      class="tab-button"
      class:active={activeTab === tab.id}
      class:disabled={tab.disabled}
      disabled={tab.disabled}
      on:click={() => handleTabClick(tab.id)}
      on:keydown={(e) => handleKeyDown(e, tab.id)}
      type="button"
      role="tab"
      aria-selected={activeTab === tab.id}
      tabindex={activeTab === tab.id ? 0 : -1}
    >
      {tab.label}
    </button>
  {/each}
</div>

<style lang="less">
  .tab-navigation {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 2rem;
    role: tablist;

    .tab-button {
      background: none;
      border: none;
      padding: 1rem 2rem;
      cursor: pointer;
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--grey);
      border-bottom: 2px solid transparent;
      transition: all 0.2s;
      position: relative;

      &:hover:not(:disabled) {
        color: var(--primary);
        background: var(--bg);
      }

      &.active {
        color: var(--primary);
        border-bottom-color: var(--primary);
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:focus {
        outline: 2px solid var(--primary);
        outline-offset: -2px;
      }
    }
  }

  @media (max-width: 768px) {
    .tab-navigation {
      .tab-button {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
      }
    }
  }
</style>
